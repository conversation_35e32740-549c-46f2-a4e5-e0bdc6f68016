import React, { useState, useEffect } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaGift, FaUsers, FaCoins, FaEdit, FaSave, FaTimes } from 'react-icons/fa';
import { toast } from 'react-toastify';
import ClaimRewardsModal from './ClaimRewardsModal';
import {
  saveReferralCode,
  getRewardsData,
  generatePersonalReferralCode,
  validateReferralCode,
  updatePersonalReferralCode
} from '../api/rewards_api';

const Dashboard = () => {
  const { user } = usePrivy();
  const [referralCode, setReferralCode] = useState('');
  const [savedReferralCode, setSavedReferralCode] = useState('');
  const [personalReferralCode, setPersonalReferralCode] = useState('');
  const [copiedLink, setCopiedLink] = useState(false);
  const [unclaimedRewards, setUnclaimedRewards] = useState(0);
  const [totalReferrals, setTotalReferrals] = useState(0);
  const [totalEarned, setTotalEarned] = useState(0);
  const [isClaimModalOpen, setIsClaimModalOpen] = useState(false);
  const [isLoadingRewards, setIsLoadingRewards] = useState(false);
  const [isEditingReferralCode, setIsEditingReferralCode] = useState(false);
  const [editedReferralCode, setEditedReferralCode] = useState('');
  const [isSavingReferralCode, setIsSavingReferralCode] = useState(false);

  // Generate personal referral code and load rewards data
  useEffect(() => {
    if (user?.id) {
      const refCode = generatePersonalReferralCode(user.id);
      setPersonalReferralCode(refCode);
      loadRewardsData();
    }
  }, [user]);

  const loadRewardsData = async () => {
    if (!user?.id) return;

    setIsLoadingRewards(true);
    try {
      const response = await getRewardsData(user.id);
      if (response.success && response.data) {
        setUnclaimedRewards(response.data.unclaimedRewards);
        setTotalEarned(response.data.totalEarned);
        setTotalReferrals(response.data.totalReferrals);
      }
    } catch (error) {
      console.error('Error loading rewards data:', error);
    } finally {
      setIsLoadingRewards(false);
    }
  };

  // Load saved referral code from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('redfyn_referral_code');
    if (saved) {
      setSavedReferralCode(saved);
    }
  }, []);

  const handleSaveReferralCode = async () => {
    if (!referralCode.trim()) {
      toast.error('Please enter a referral code');
      return;
    }

    if (!validateReferralCode(referralCode.trim())) {
      toast.error('Referral code must be 6-12 alphanumeric characters');
      return;
    }

    if (!user?.id) {
      toast.error('User not authenticated');
      return;
    }

    try {
      const response = await saveReferralCode(user.id, referralCode.trim());
      if (response.success) {
        localStorage.setItem('redfyn_referral_code', referralCode.trim().toUpperCase());
        setSavedReferralCode(referralCode.trim().toUpperCase());
        setReferralCode('');
        toast.success('Referral code saved successfully!');
        // Reload rewards data to reflect any changes
        loadRewardsData();
      } else {
        toast.error(response.error || 'Failed to save referral code');
      }
    } catch (error) {
      console.error('Error saving referral code:', error);
      toast.error('Failed to save referral code. Please try again.');
    }
  };

  const handleCopyReferralLink = async () => {
    const currentCode = isEditingReferralCode && editedReferralCode ? editedReferralCode : personalReferralCode;
    const referralLink = `https://redfyn.crypfi.io/ref/${currentCode}`;

    try {
      await navigator.clipboard.writeText(referralLink);
      setCopiedLink(true);
      if (isEditingReferralCode && editedReferralCode) {
        toast.success('Preview link copied! Remember to save your changes.');
      } else {
        toast.success('Referral link copied to clipboard!');
      }

      setTimeout(() => {
        setCopiedLink(false);
      }, 2000);
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  const handleClaimRewards = () => {
    if (unclaimedRewards <= 0) {
      toast.info('No rewards available to claim');
      return;
    }

    setIsClaimModalOpen(true);
  };

  const handleClaimSuccess = (transactionHash: string, amount: number) => {
    // Update local state to reflect claimed rewards
    setUnclaimedRewards(prev => prev - amount);
    setTotalEarned(prev => prev + amount);

    // Reload rewards data to get updated information
    loadRewardsData();
  };

  const handleEditReferralCode = () => {
    setEditedReferralCode(personalReferralCode);
    setIsEditingReferralCode(true);
  };

  const handleCancelEditReferralCode = () => {
    setEditedReferralCode('');
    setIsEditingReferralCode(false);
  };

  const handleSaveReferralCode = async () => {
    if (!editedReferralCode.trim()) {
      toast.error('Please enter a referral code');
      return;
    }

    if (!validateReferralCode(editedReferralCode.trim())) {
      toast.error('Referral code must be 6-12 alphanumeric characters');
      return;
    }

    if (editedReferralCode.trim().toUpperCase() === personalReferralCode) {
      toast.info('No changes made to referral code');
      setIsEditingReferralCode(false);
      return;
    }

    if (!user?.id) {
      toast.error('User not authenticated');
      return;
    }

    setIsSavingReferralCode(true);

    try {
      const response = await updatePersonalReferralCode(user.id, editedReferralCode.trim());
      if (response.success && response.data) {
        setPersonalReferralCode(response.data.referralCode);
        setIsEditingReferralCode(false);
        setEditedReferralCode('');
        toast.success('Referral code updated successfully!');
        // Reload rewards data to reflect any changes
        loadRewardsData();
      } else {
        toast.error(response.error || 'Failed to update referral code');
      }
    } catch (error) {
      console.error('Error updating referral code:', error);
      toast.error('Failed to update referral code. Please try again.');
    } finally {
      setIsSavingReferralCode(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaCoins className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Unclaimed Rewards</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">${unclaimedRewards.toFixed(2)}</div>
          <p className="text-gray-400 text-sm">Available to claim</p>
        </div>

        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUsers className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Total Referrals</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">{totalReferrals}</div>
          <p className="text-gray-400 text-sm">Active referrals</p>
        </div>

        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaGift className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Total Earned</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">${totalEarned.toFixed(2)}</div>
          <p className="text-gray-400 text-sm">All-time earnings</p>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Referral Code Input Section */}
        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <h3 className="text-xl font-semibold text-white mb-4">Enter Referral Code</h3>
          <p className="text-gray-400 text-sm mb-6">
            Have a referral code? Enter it here to link your account and start earning rewards.
          </p>
          
          {savedReferralCode ? (
            <div className="bg-[#7FFFD4]/10 border border-[#7FFFD4]/30 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <FaCheck className="text-[#7FFFD4]" />
                <span className="text-white font-medium">Referral Code Active</span>
              </div>
              <p className="text-gray-300">Code: <span className="font-mono text-[#7FFFD4]">{savedReferralCode}</span></p>
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <input
                  type="text"
                  value={referralCode}
                  onChange={(e) => setReferralCode(e.target.value.toUpperCase())}
                  placeholder="Enter referral code"
                  className="w-full px-4 py-3 bg-[#0F1419] border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#7FFFD4] transition-colors"
                />
              </div>
              <button
                onClick={handleSaveReferralCode}
                className="w-full px-6 py-3 bg-[#7FFFD4] text-black font-semibold rounded-lg hover:bg-[#7FFFD4]/90 transition-colors"
              >
                Save Referral Code
              </button>
            </div>
          )}
        </div>

        {/* Personal Referral Link */}
        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <h3 className="text-xl font-semibold text-white mb-4">Your Referral Link</h3>
          <p className="text-gray-400 text-sm mb-6">
            Share this link with friends to earn rewards when they trade.
          </p>
          
          <div className="space-y-4">
            <div className="bg-[#0F1419] border border-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <p className="text-gray-400 text-sm">Your Referral Code:</p>
                {!isEditingReferralCode && (
                  <button
                    onClick={handleEditReferralCode}
                    className="flex items-center gap-1 text-[#7FFFD4] hover:text-[#7FFFD4]/80 transition-colors text-sm"
                  >
                    <FaEdit size={12} />
                    Edit
                  </button>
                )}
              </div>

              {isEditingReferralCode ? (
                <div className="space-y-3">
                  <div className="relative">
                    <input
                      type="text"
                      value={editedReferralCode}
                      onChange={(e) => setEditedReferralCode(e.target.value.toUpperCase())}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && editedReferralCode && validateReferralCode(editedReferralCode)) {
                          handleSaveReferralCode();
                        } else if (e.key === 'Escape') {
                          handleCancelEditReferralCode();
                        }
                      }}
                      placeholder="Enter new referral code"
                      className="w-full px-3 py-2 bg-[#181C20] border border-gray-600 rounded-lg text-white font-mono text-lg placeholder-gray-400 focus:outline-none focus:border-[#7FFFD4] transition-colors"
                      maxLength={12}
                      autoFocus
                    />
                    <div className="absolute right-3 top-2 text-xs text-gray-400">
                      {editedReferralCode.length}/12
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={handleSaveReferralCode}
                      disabled={isSavingReferralCode || !editedReferralCode || !validateReferralCode(editedReferralCode)}
                      className={`flex-1 px-4 py-2 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2 ${
                        isSavingReferralCode || !editedReferralCode || !validateReferralCode(editedReferralCode)
                          ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                          : 'bg-[#7FFFD4] text-black hover:bg-[#7FFFD4]/90'
                      }`}
                    >
                      <FaSave size={12} />
                      {isSavingReferralCode ? 'Saving...' : 'Save'}
                    </button>
                    <button
                      onClick={handleCancelEditReferralCode}
                      disabled={isSavingReferralCode}
                      className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center gap-2"
                    >
                      <FaTimes size={12} />
                      Cancel
                    </button>
                  </div>
                  <div className="space-y-1">
                    <p className="text-gray-400 text-xs">
                      Code must be 6-12 alphanumeric characters and unique
                    </p>
                    {editedReferralCode && !validateReferralCode(editedReferralCode) && (
                      <p className="text-red-400 text-xs">
                        {editedReferralCode.length < 6
                          ? 'Code must be at least 6 characters'
                          : 'Code can only contain letters and numbers'
                        }
                      </p>
                    )}
                    {editedReferralCode && validateReferralCode(editedReferralCode) && editedReferralCode !== personalReferralCode && (
                      <p className="text-green-400 text-xs">
                        ✓ Valid format
                      </p>
                    )}
                    {editedReferralCode && editedReferralCode === personalReferralCode && (
                      <p className="text-yellow-400 text-xs">
                        This is your current code
                      </p>
                    )}
                  </div>
                </div>
              ) : (
                <p className="font-mono text-[#7FFFD4] text-lg">{personalReferralCode}</p>
              )}
            </div>
            
            <div className="bg-[#0F1419] border border-gray-700 rounded-lg p-4">
              <p className="text-gray-400 text-sm mb-2">Your Referral Link:</p>
              <p className="font-mono text-white text-sm break-all">
                https://redfyn.crypfi.io/ref/{isEditingReferralCode && editedReferralCode ? editedReferralCode : personalReferralCode}
              </p>
              {isEditingReferralCode && editedReferralCode && (
                <p className="text-yellow-400 text-xs mt-2">
                  Preview: Link will update when you save the new code
                </p>
              )}
            </div>
            
            <button
              onClick={handleCopyReferralLink}
              disabled={isEditingReferralCode && !editedReferralCode}
              className={`w-full px-6 py-3 font-semibold rounded-lg transition-all duration-300 flex items-center justify-center gap-2 ${
                isEditingReferralCode && !editedReferralCode
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : 'bg-[#214638] text-white hover:bg-[#14FFA2] hover:text-black'
              }`}
            >
              {copiedLink ? <FaCheck /> : <FaCopy />}
              {copiedLink ? 'Copied!' : isEditingReferralCode && editedReferralCode ? 'Copy Preview Link' : 'Copy Referral Link'}
            </button>
          </div>
        </div>
      </div>

      {/* Claim Rewards Section */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-semibold text-white mb-2">Claim Your Rewards</h3>
            <p className="text-gray-400 text-sm">
              You have <span className="text-[#7FFFD4] font-semibold">${unclaimedRewards.toFixed(2)}</span> in unclaimed rewards
            </p>
          </div>
          <button
            onClick={handleClaimRewards}
            disabled={unclaimedRewards <= 0}
            className={`px-8 py-3 font-semibold rounded-lg transition-colors ${
              unclaimedRewards > 0
                ? 'bg-[#7FFFD4] text-black hover:bg-[#7FFFD4]/90'
                : 'bg-gray-600 text-gray-400 cursor-not-allowed'
            }`}
          >
            Claim Rewards
          </button>
        </div>
        
        {unclaimedRewards <= 0 && (
          <div className="bg-[#0F1419] border border-gray-700 rounded-lg p-4">
            <p className="text-gray-400 text-center">
              No rewards available to claim. Start referring friends to earn rewards!
            </p>
          </div>
        )}
      </div>

      {/* Claim Rewards Modal */}
      <ClaimRewardsModal
        isOpen={isClaimModalOpen}
        onClose={() => setIsClaimModalOpen(false)}
        unclaimedAmount={unclaimedRewards}
        onClaimSuccess={handleClaimSuccess}
      />
    </div>
  );
};

export default Dashboard;
