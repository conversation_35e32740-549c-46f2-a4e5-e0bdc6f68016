import React, { useState, useEffect } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { FaUser, FaUsers, FaChevronDown, FaChevronRight, FaSpinner } from 'react-icons/fa';
import { getRewardsData, ReferralLevel, ReferralUser } from '../api/rewards_api';

// Interfaces are now imported from rewards_api

const ReferralTree = () => {
  const { user } = usePrivy();
  const [expandedLevels, setExpandedLevels] = useState<Set<number>>(new Set([1]));
  const [referralLevels, setReferralLevels] = useState<ReferralLevel[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load referral tree data
  useEffect(() => {
    if (user?.id) {
      loadReferralData();
    }
  }, [user]);

  const loadReferralData = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const response = await getRewardsData(user.id);
      if (response.success && response.data) {
        setReferralLevels(response.data.referralLevels || []);
      } else {
        // Fallback to empty levels with commission structure
        const emptyLevels: ReferralLevel[] = [
          { level: 1, commission: 40, count: 0, earnings: 0, referrals: [] },
          { level: 2, commission: 15, count: 0, earnings: 0, referrals: [] },
          { level: 3, commission: 10, count: 0, earnings: 0, referrals: [] },
          { level: 4, commission: 5, count: 0, earnings: 0, referrals: [] },
          { level: 5, commission: 2, count: 0, earnings: 0, referrals: [] },
        ];
        setReferralLevels(emptyLevels);
      }
    } catch (error) {
      console.error('Error loading referral data:', error);
      // Fallback to empty levels
      const emptyLevels: ReferralLevel[] = [
        { level: 1, commission: 40, count: 0, earnings: 0, referrals: [] },
        { level: 2, commission: 15, count: 0, earnings: 0, referrals: [] },
        { level: 3, commission: 10, count: 0, earnings: 0, referrals: [] },
        { level: 4, commission: 5, count: 0, earnings: 0, referrals: [] },
        { level: 5, commission: 2, count: 0, earnings: 0, referrals: [] },
      ];
      setReferralLevels(emptyLevels);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleLevel = (level: number) => {
    const newExpanded = new Set(expandedLevels);
    if (newExpanded.has(level)) {
      newExpanded.delete(level);
    } else {
      newExpanded.add(level);
    }
    setExpandedLevels(newExpanded);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTotalEarnings = () => {
    return referralLevels.reduce((total, level) => total + level.earnings, 0);
  };

  const getTotalReferrals = () => {
    return referralLevels.reduce((total, level) => total + level.count, 0);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <FaSpinner className="animate-spin text-[#7FFFD4] text-3xl mr-4" />
        <span className="text-white text-lg">Loading referral network...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUsers className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Total Network</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">{getTotalReferrals()}</div>
          <p className="text-gray-400 text-sm">Referrals across all levels</p>
        </div>

        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUser className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Network Earnings</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">${getTotalEarnings().toFixed(2)}</div>
          <p className="text-gray-400 text-sm">Total from referral network</p>
        </div>

        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUsers className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Active Levels</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">{referralLevels.filter(l => l.count > 0).length}</div>
          <p className="text-gray-400 text-sm">Levels with referrals</p>
        </div>
      </div>

      {/* Commission Structure */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
        <h3 className="text-xl font-semibold text-white mb-6">Commission Structure</h3>
        <div className="grid grid-cols-5 gap-4">
          {[40, 15, 10, 5, 2].map((commission, index) => (
            <div key={index} className="text-center">
              <div className="bg-[#0F1419] border border-gray-700 rounded-lg p-4 mb-2">
                <div className="text-2xl font-bold text-[#7FFFD4] mb-1">{commission}%</div>
                <div className="text-sm text-gray-400">Level {index + 1}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Visual Referral Tree */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-8 border border-gray-800/50">
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold text-white mb-2">Referral Network</h3>
          <p className="text-gray-400 text-sm">Your multi-level referral structure</p>
        </div>

        <div className="flex flex-col items-center space-y-6 overflow-x-auto min-w-max py-4">
          {/* You (Root) */}
          <div className="flex flex-col items-center">
            <div className="w-20 h-20 bg-[#7FFFD4] rounded-full flex items-center justify-center relative shadow-lg border-4 border-[#7FFFD4]/20">
              <FaUser className="text-black text-2xl" />
              <div className="absolute -top-10 text-white font-bold text-lg bg-[#141416] px-3 py-1 rounded-lg border border-[#7FFFD4]/30">
                You
              </div>
            </div>
          </div>

          {/* Level 1 */}
          {referralLevels[0] && referralLevels[0].count > 0 && (
            <>
              {/* Connecting Line */}
              <div className="w-1 h-12 bg-[#7FFFD4] rounded-full shadow-sm"></div>

              <div className="flex flex-col items-center">
                <div className="flex items-center gap-4 mb-4">
                  <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg border border-orange-400/30">
                    40%
                  </div>
                  <div className="text-yellow-400 font-bold text-xl">#1</div>
                </div>

                <div className="flex gap-6">
                  {Array.from({ length: Math.min(referralLevels[0].count, 5) }).map((_, index) => (
                    <div key={index} className="w-14 h-14 bg-[#7FFFD4] rounded-full flex items-center justify-center shadow-lg border-2 border-[#7FFFD4]/30 hover:scale-105 transition-transform">
                      <FaUser className="text-black text-lg" />
                    </div>
                  ))}
                  {referralLevels[0].count > 5 && (
                    <div className="w-14 h-14 bg-[#7FFFD4]/30 rounded-full flex items-center justify-center border-2 border-[#7FFFD4]/50">
                      <span className="text-white text-sm font-bold">+{referralLevels[0].count - 5}</span>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Level 2 */}
          {referralLevels[1] && referralLevels[1].count > 0 && (
            <>
              <div className="w-1 h-12 bg-[#7FFFD4] rounded-full shadow-sm"></div>

              <div className="flex flex-col items-center">
                <div className="flex items-center gap-4 mb-4">
                  <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg border border-orange-400/30">
                    15%
                  </div>
                  <div className="text-yellow-400 font-bold text-xl">#2</div>
                </div>

                <div className="flex gap-5">
                  {Array.from({ length: Math.min(referralLevels[1].count, 6) }).map((_, index) => (
                    <div key={index} className="w-12 h-12 bg-[#7FFFD4] rounded-full flex items-center justify-center shadow-lg border-2 border-[#7FFFD4]/30 hover:scale-105 transition-transform">
                      <FaUser className="text-black text-sm" />
                    </div>
                  ))}
                  {referralLevels[1].count > 6 && (
                    <div className="w-12 h-12 bg-[#7FFFD4]/30 rounded-full flex items-center justify-center border-2 border-[#7FFFD4]/50">
                      <span className="text-white text-xs font-bold">+{referralLevels[1].count - 6}</span>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Level 3 */}
          {referralLevels[2] && referralLevels[2].count > 0 && (
            <>
              <div className="w-1 h-12 bg-[#7FFFD4] rounded-full shadow-sm"></div>

              <div className="flex flex-col items-center">
                <div className="flex items-center gap-4 mb-4">
                  <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg border border-orange-400/30">
                    10%
                  </div>
                  <div className="text-yellow-400 font-bold text-xl">#3</div>
                </div>

                <div className="flex gap-3">
                  {Array.from({ length: Math.min(referralLevels[2].count, 8) }).map((_, index) => (
                    <div key={index} className="w-10 h-10 bg-[#7FFFD4] rounded-full flex items-center justify-center shadow-md border border-[#7FFFD4]/30 hover:scale-105 transition-transform">
                      <FaUser className="text-black text-xs" />
                    </div>
                  ))}
                  {referralLevels[2].count > 8 && (
                    <div className="w-10 h-10 bg-[#7FFFD4]/30 rounded-full flex items-center justify-center border border-[#7FFFD4]/50">
                      <span className="text-white text-xs font-bold">+{referralLevels[2].count - 8}</span>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Level 4 */}
          {referralLevels[3] && referralLevels[3].count > 0 && (
            <>
              <div className="w-1 h-12 bg-[#7FFFD4] rounded-full shadow-sm"></div>

              <div className="flex flex-col items-center">
                <div className="flex items-center gap-4 mb-4">
                  <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg border border-orange-400/30">
                    5%
                  </div>
                  <div className="text-yellow-400 font-bold text-xl">#4</div>
                </div>

                <div className="flex gap-2">
                  {Array.from({ length: Math.min(referralLevels[3].count, 10) }).map((_, index) => (
                    <div key={index} className="w-9 h-9 bg-[#7FFFD4] rounded-full flex items-center justify-center shadow-md border border-[#7FFFD4]/30 hover:scale-105 transition-transform">
                      <FaUser className="text-black text-xs" />
                    </div>
                  ))}
                  {referralLevels[3].count > 10 && (
                    <div className="w-9 h-9 bg-[#7FFFD4]/30 rounded-full flex items-center justify-center border border-[#7FFFD4]/50">
                      <span className="text-white text-xs font-bold">+{referralLevels[3].count - 10}</span>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Level 5 */}
          {referralLevels[4] && referralLevels[4].count > 0 && (
            <>
              <div className="w-1 h-12 bg-[#7FFFD4] rounded-full shadow-sm"></div>

              <div className="flex flex-col items-center">
                <div className="flex items-center gap-4 mb-4">
                  <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg border border-orange-400/30">
                    2%
                  </div>
                  <div className="text-yellow-400 font-bold text-xl">#5</div>
                </div>

                <div className="flex gap-1.5 flex-wrap justify-center max-w-lg">
                  {Array.from({ length: Math.min(referralLevels[4].count, 15) }).map((_, index) => (
                    <div key={index} className="w-8 h-8 bg-[#7FFFD4] rounded-full flex items-center justify-center shadow-sm border border-[#7FFFD4]/30 hover:scale-105 transition-transform">
                      <FaUser className="text-black text-xs" />
                    </div>
                  ))}
                  {referralLevels[4].count > 15 && (
                    <div className="w-8 h-8 bg-[#7FFFD4]/30 rounded-full flex items-center justify-center border border-[#7FFFD4]/50">
                      <span className="text-white text-xs font-bold">+{referralLevels[4].count - 15}</span>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Empty State */}
          {referralLevels.every(level => level.count === 0) && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4 text-lg">No referrals yet</div>
              <div className="text-sm text-gray-500 mb-6">Share your referral link to start building your network!</div>
              <div className="text-xs text-gray-600">
                Earn commissions: 40% → 15% → 10% → 5% → 2%
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Detailed Level Information */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
        <h3 className="text-xl font-semibold text-white mb-6">Detailed Network Information</h3>

        <div className="space-y-4">
          {referralLevels.map((level) => (
            <div key={level.level} className="border border-gray-700 rounded-lg overflow-hidden">
              {/* Level Header */}
              <button
                onClick={() => toggleLevel(level.level)}
                className="w-full px-6 py-4 bg-[#0F1419] hover:bg-[#1A1F24] transition-colors flex items-center justify-between"
              >
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {expandedLevels.has(level.level) ? (
                      <FaChevronDown className="text-gray-400" />
                    ) : (
                      <FaChevronRight className="text-gray-400" />
                    )}
                    <span className="text-white font-semibold">Level {level.level}</span>
                  </div>
                  <div className="flex items-center gap-6">
                    <span className="text-[#7FFFD4] font-semibold">{level.commission}% Commission</span>
                    <span className="text-gray-300">{level.count} Referrals</span>
                    <span className="text-gray-300">${level.earnings.toFixed(2)} Earned</span>
                  </div>
                </div>
              </button>

              {/* Level Content */}
              {expandedLevels.has(level.level) && (
                <div className="p-6 bg-[#141416]">
                  {level.referrals.length > 0 ? (
                    <div className="grid gap-4">
                      {level.referrals.map((referral) => (
                        <div
                          key={referral.id}
                          className="flex items-center justify-between p-4 bg-[#181C20] rounded-lg border border-gray-700"
                        >
                          <div className="flex items-center gap-4">
                            <div className="w-10 h-10 bg-[#7FFFD4]/10 rounded-full flex items-center justify-center">
                              <FaUser className="text-[#7FFFD4]" />
                            </div>
                            <div>
                              <div className="text-white font-medium">{referral.username}</div>
                              <div className="text-gray-400 text-sm">Joined {formatDate(referral.joinDate)}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-white font-semibold">${referral.totalEarnings.toFixed(2)}</div>
                            <div className={`text-sm ${referral.isActive ? 'text-[#7FFFD4]' : 'text-gray-400'}`}>
                              {referral.isActive ? 'Active' : 'Inactive'}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-gray-400 mb-2">No referrals at this level yet</div>
                      <div className="text-sm text-gray-500">Share your referral link to grow your network!</div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ReferralTree;
