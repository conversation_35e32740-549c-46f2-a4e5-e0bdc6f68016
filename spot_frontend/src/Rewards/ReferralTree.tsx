import React, { useState, useEffect } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { FaUser, FaUsers, FaChevronDown, FaChevronRight, FaSpinner } from 'react-icons/fa';
import { getRewardsData, ReferralLevel, ReferralUser } from '../api/rewards_api';

// Interfaces are now imported from rewards_api

const ReferralTree = () => {
  const { user } = usePrivy();
  const [expandedLevels, setExpandedLevels] = useState<Set<number>>(new Set([1]));
  const [referralLevels, setReferralLevels] = useState<ReferralLevel[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load referral tree data
  useEffect(() => {
    if (user?.id) {
      loadReferralData();
    }
  }, [user]);

  // Simplified approach - no complex tree building needed

  const loadReferralData = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const response = await getRewardsData(user.id);
      if (response.success && response.data) {
        setReferralLevels(response.data.referralLevels || []);
      } else {
        // Fallback to mock data for demonstration
        const mockLevels: ReferralLevel[] = [
          { level: 1, commission: 40, count: 3, earnings: 450.25, referrals: [] },
          { level: 2, commission: 15, count: 4, earnings: 125.80, referrals: [] },
          { level: 3, commission: 10, count: 2, earnings: 67.40, referrals: [] },
          { level: 4, commission: 5, count: 0, earnings: 0, referrals: [] },
          { level: 5, commission: 2, count: 0, earnings: 0, referrals: [] },
        ];
        setReferralLevels(mockLevels);
      }
    } catch (error) {
      console.error('Error loading referral data:', error);
      // Fallback to empty levels
      const emptyLevels: ReferralLevel[] = [
        { level: 1, commission: 40, count: 0, earnings: 0, referrals: [] },
        { level: 2, commission: 15, count: 0, earnings: 0, referrals: [] },
        { level: 3, commission: 10, count: 0, earnings: 0, referrals: [] },
        { level: 4, commission: 5, count: 0, earnings: 0, referrals: [] },
        { level: 5, commission: 2, count: 0, earnings: 0, referrals: [] },
      ];
      setReferralLevels(emptyLevels);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleLevel = (level: number) => {
    const newExpanded = new Set(expandedLevels);
    if (newExpanded.has(level)) {
      newExpanded.delete(level);
    } else {
      newExpanded.add(level);
    }
    setExpandedLevels(newExpanded);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTotalEarnings = () => {
    return referralLevels.reduce((total, level) => total + level.earnings, 0);
  };

  const getTotalReferrals = () => {
    return referralLevels.reduce((total, level) => total + level.count, 0);
  };

  // Simplified approach - no complex tree component needed

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <FaSpinner className="animate-spin text-[#7FFFD4] text-3xl mr-4" />
        <span className="text-white text-lg">Loading referral network...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUsers className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Total Network</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">{getTotalReferrals()}</div>
          <p className="text-gray-400 text-sm">Referrals across all levels</p>
        </div>

        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUser className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Network Earnings</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">${getTotalEarnings().toFixed(2)}</div>
          <p className="text-gray-400 text-sm">Total from referral network</p>
        </div>

        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUsers className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Active Levels</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">{referralLevels.filter(l => l.count > 0).length}</div>
          <p className="text-gray-400 text-sm">Levels with referrals</p>
        </div>
      </div>

      {/* Commission Structure */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
        <h3 className="text-xl font-semibold text-white mb-6">Commission Structure</h3>
        <div className="grid grid-cols-5 gap-4">
          {[40, 15, 10, 5, 2].map((commission, index) => (
            <div key={index} className="text-center">
              <div className="bg-[#0F1419] border border-gray-700 rounded-lg p-4 mb-2">
                <div className="text-2xl font-bold text-[#7FFFD4] mb-1">{commission}%</div>
                <div className="text-sm text-gray-400">Level {index + 1}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Modern Professional Referral Tree */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl border border-gray-800/50 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-[#1a1d23] to-[#1f2329] px-8 py-6 border-b border-gray-700/50">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white mb-2">Referral Network</h3>
            <p className="text-gray-400 text-sm">Your multi-level referral structure</p>
          </div>
        </div>

        {/* Commission Legend */}
        <div className="px-8 py-6 bg-[#0f1419]/50 border-b border-gray-700/30">
          <div className="flex justify-center">
            <div className="inline-flex items-center gap-6 bg-[#141416] px-6 py-3 rounded-full border border-gray-700/50">
              {[
                { level: 1, commission: 40, color: '#ff6b35' },
                { level: 2, commission: 15, color: '#ff8c42' },
                { level: 3, commission: 10, color: '#ffa726' },
                { level: 4, commission: 5, color: '#ffcc02' },
                { level: 5, commission: 2, color: '#fff176' }
              ].map(({ level, commission, color }, index) => (
                <div key={level} className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full shadow-sm"
                    style={{ backgroundColor: color }}
                  ></div>
                  <span className="text-white text-sm font-medium">{commission}%</span>
                  {index < 4 && <div className="w-px h-4 bg-gray-600 ml-2"></div>}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Tree Container */}
        <div className="p-8">
          <div className="overflow-x-auto">
            <div className="min-w-max">
              {/* Tree Structure */}
              <div className="relative">
                {/* SVG for connections */}
                <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{ zIndex: 1 }}>
                  <defs>
                    <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" style={{ stopColor: '#7FFFD4', stopOpacity: 0.8 }} />
                      <stop offset="100%" style={{ stopColor: '#7FFFD4', stopOpacity: 0.3 }} />
                    </linearGradient>
                  </defs>
                </svg>

                {/* Tree Nodes */}
                <div className="relative" style={{ zIndex: 2 }}>
                  <div className="flex flex-col items-center">
                    {/* Root Node */}
                    <div className="flex flex-col items-center mb-16">
                      <div className="relative">
                        <div className="w-24 h-24 bg-gradient-to-br from-[#7FFFD4] to-[#4fd1c7] rounded-2xl flex items-center justify-center shadow-2xl border-4 border-[#7FFFD4]/20 transform hover:scale-105 transition-all duration-300">
                          <FaUser className="text-black text-3xl" />
                        </div>
                        <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2">
                          <div className="bg-[#141416] px-4 py-2 rounded-lg border border-[#7FFFD4]/30 shadow-lg">
                            <span className="text-white font-bold text-lg">You</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Levels */}
                    {referralLevels.map((level, index) => {
                      if (level.count === 0) return null;

                      const colors = [
                        { bg: 'from-orange-500 to-orange-600', text: 'text-white', border: 'border-orange-400/30' },
                        { bg: 'from-orange-400 to-orange-500', text: 'text-white', border: 'border-orange-300/30' },
                        { bg: 'from-yellow-500 to-orange-400', text: 'text-white', border: 'border-yellow-400/30' },
                        { bg: 'from-yellow-400 to-yellow-500', text: 'text-black', border: 'border-yellow-300/30' },
                        { bg: 'from-yellow-300 to-yellow-400', text: 'text-black', border: 'border-yellow-200/30' }
                      ];

                      return (
                        <div key={level.level} className="flex flex-col items-center mb-16 last:mb-0">
                          {/* Connection Line */}
                          <div className="w-1 h-16 bg-gradient-to-b from-[#7FFFD4] to-[#7FFFD4]/50 rounded-full mb-8 shadow-sm"></div>

                          {/* Level Info */}
                          <div className="flex items-center gap-6 mb-8">
                            <div className={`bg-gradient-to-r ${colors[index].bg} ${colors[index].text} px-6 py-3 rounded-full font-bold text-lg shadow-xl ${colors[index].border} border-2`}>
                              {level.commission}%
                            </div>
                            <div className="text-yellow-400 font-bold text-2xl">
                              #{level.level}
                            </div>
                          </div>

                          {/* Level Node */}
                          <div className="relative group">
                            <div className="w-20 h-20 bg-gradient-to-br from-[#7FFFD4] to-[#4fd1c7] rounded-xl flex items-center justify-center shadow-xl border-2 border-[#7FFFD4]/30 transform group-hover:scale-110 transition-all duration-300 cursor-pointer">
                              <span className="text-black font-bold text-2xl">{level.count}</span>
                            </div>

                            {/* Hover Tooltip */}
                            <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                              <div className="bg-[#141416] px-4 py-2 rounded-lg border border-[#7FFFD4]/30 shadow-xl whitespace-nowrap">
                                <div className="text-white text-sm font-medium">Level {level.level}</div>
                                <div className="text-[#7FFFD4] text-xs">{level.count} referral{level.count !== 1 ? 's' : ''}</div>
                                <div className="text-gray-400 text-xs">${level.earnings.toFixed(2)} earned</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}

                    {/* Empty State */}
                    {referralLevels.every(level => level.count === 0) && (
                      <div className="text-center py-16">
                        <div className="w-32 h-32 bg-gradient-to-br from-gray-600 to-gray-700 rounded-2xl flex items-center justify-center mx-auto mb-6 opacity-50">
                          <FaUsers className="text-gray-400 text-4xl" />
                        </div>
                        <div className="text-gray-400 mb-4 text-xl font-semibold">No referrals yet</div>
                        <div className="text-gray-500 mb-8 max-w-md mx-auto">
                          Share your referral link to start building your network and earning commissions!
                        </div>
                        <div className="inline-flex items-center gap-2 bg-[#141416] px-6 py-3 rounded-full border border-gray-700/50">
                          <span className="text-gray-400 text-sm">Earn up to</span>
                          <span className="text-[#7FFFD4] font-bold text-sm">40% commission</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Level Information */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
        <h3 className="text-xl font-semibold text-white mb-6">Detailed Network Information</h3>

        <div className="space-y-4">
          {referralLevels.map((level) => (
            <div key={level.level} className="border border-gray-700 rounded-lg overflow-hidden">
              {/* Level Header */}
              <button
                onClick={() => toggleLevel(level.level)}
                className="w-full px-6 py-4 bg-[#0F1419] hover:bg-[#1A1F24] transition-colors flex items-center justify-between"
              >
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {expandedLevels.has(level.level) ? (
                      <FaChevronDown className="text-gray-400" />
                    ) : (
                      <FaChevronRight className="text-gray-400" />
                    )}
                    <span className="text-white font-semibold">Level {level.level}</span>
                  </div>
                  <div className="flex items-center gap-6">
                    <span className="text-[#7FFFD4] font-semibold">{level.commission}% Commission</span>
                    <span className="text-gray-300">{level.count} Referrals</span>
                    <span className="text-gray-300">${level.earnings.toFixed(2)} Earned</span>
                  </div>
                </div>
              </button>

              {/* Level Content */}
              {expandedLevels.has(level.level) && (
                <div className="p-6 bg-[#141416]">
                  {level.referrals.length > 0 ? (
                    <div className="grid gap-4">
                      {level.referrals.map((referral) => (
                        <div
                          key={referral.id}
                          className="flex items-center justify-between p-4 bg-[#181C20] rounded-lg border border-gray-700"
                        >
                          <div className="flex items-center gap-4">
                            <div className="w-10 h-10 bg-[#7FFFD4]/10 rounded-full flex items-center justify-center">
                              <FaUser className="text-[#7FFFD4]" />
                            </div>
                            <div>
                              <div className="text-white font-medium">{referral.username}</div>
                              <div className="text-gray-400 text-sm">Joined {formatDate(referral.joinDate)}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-white font-semibold">${referral.totalEarnings.toFixed(2)}</div>
                            <div className={`text-sm ${referral.isActive ? 'text-[#7FFFD4]' : 'text-gray-400'}`}>
                              {referral.isActive ? 'Active' : 'Inactive'}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-gray-400 mb-2">No referrals at this level yet</div>
                      <div className="text-sm text-gray-500">Share your referral link to grow your network!</div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ReferralTree;
