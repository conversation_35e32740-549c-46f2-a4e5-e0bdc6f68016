import React, { useState, useEffect } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { Fa<PERSON>ser, FaUsers, FaChevronDown, FaChevronRight, FaSpinner, FaMinus, FaPlus } from 'react-icons/fa';
import { getRewardsData, ReferralLevel, ReferralUser } from '../api/rewards_api';

// Enhanced interfaces for tree structure
interface TreeNode {
  id: string;
  username: string;
  level: number;
  commission: number;
  earnings: number;
  isActive: boolean;
  joinDate: string;
  children: TreeNode[];
  isExpanded: boolean;
  parentId?: string;
}

// Interfaces are now imported from rewards_api

const ReferralTree = () => {
  const { user } = usePrivy();
  const [expandedLevels, setExpandedLevels] = useState<Set<number>>(new Set([1]));
  const [referralLevels, setReferralLevels] = useState<ReferralLevel[]>([]);
  const [treeData, setTreeData] = useState<TreeNode | null>(null);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);

  // Load referral tree data
  useEffect(() => {
    if (user?.id) {
      loadReferralData();
    }
  }, [user]);

  // Build tree structure from referral levels
  const buildTreeStructure = (levels: ReferralLevel[]): TreeNode => {
    // Create root node (You)
    const rootNode: TreeNode = {
      id: 'root',
      username: 'You',
      level: 0,
      commission: 0,
      earnings: 0,
      isActive: true,
      joinDate: new Date().toISOString(),
      children: [],
      isExpanded: true
    };

    // Build tree level by level
    let currentLevelNodes: TreeNode[] = [rootNode];

    levels.forEach((level, levelIndex) => {
      const nextLevelNodes: TreeNode[] = [];

      // Distribute referrals across current level nodes
      const referralsPerNode = Math.ceil(level.referrals.length / Math.max(currentLevelNodes.length, 1));
      let referralIndex = 0;

      currentLevelNodes.forEach((parentNode, nodeIndex) => {
        const nodeReferrals = level.referrals.slice(
          referralIndex,
          referralIndex + referralsPerNode
        );

        nodeReferrals.forEach((referral, refIndex) => {
          const childNode: TreeNode = {
            id: `${level.level}-${referralIndex + refIndex}`,
            username: referral.username,
            level: level.level,
            commission: level.commission,
            earnings: referral.totalEarnings,
            isActive: referral.isActive,
            joinDate: referral.joinDate,
            children: [],
            isExpanded: false,
            parentId: parentNode.id
          };

          parentNode.children.push(childNode);
          nextLevelNodes.push(childNode);
        });

        referralIndex += nodeReferrals.length;
      });

      currentLevelNodes = nextLevelNodes;
    });

    return rootNode;
  };

  const loadReferralData = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const response = await getRewardsData(user.id);
      if (response.success && response.data) {
        const levels = response.data.referralLevels || [];
        setReferralLevels(levels);
        const tree = buildTreeStructure(levels);
        setTreeData(tree);
        // Auto-expand first level
        setExpandedNodes(new Set(['root']));
      } else {
        // Fallback to mock data for demonstration
        const mockLevels: ReferralLevel[] = [
          {
            level: 1,
            commission: 40,
            count: 3,
            earnings: 450.25,
            referrals: [
              { id: '1', username: 'trader_alex', joinDate: '2024-01-15', totalEarnings: 125.50, isActive: true },
              { id: '2', username: 'crypto_sarah', joinDate: '2024-01-20', totalEarnings: 89.75, isActive: true },
              { id: '3', username: 'defi_mike', joinDate: '2024-02-01', totalEarnings: 235.00, isActive: false },
            ]
          },
          {
            level: 2,
            commission: 15,
            count: 4,
            earnings: 125.80,
            referrals: [
              { id: '4', username: 'sol_trader', joinDate: '2024-02-10', totalEarnings: 45.30, isActive: true },
              { id: '5', username: 'nft_collector', joinDate: '2024-02-15', totalEarnings: 80.50, isActive: true },
              { id: '6', username: 'yield_farmer', joinDate: '2024-03-01', totalEarnings: 67.40, isActive: true },
              { id: '7', username: 'hodler_joe', joinDate: '2024-03-10', totalEarnings: 23.15, isActive: false },
            ]
          },
          {
            level: 3,
            commission: 10,
            count: 2,
            earnings: 67.40,
            referrals: [
              { id: '8', username: 'new_trader', joinDate: '2024-03-20', totalEarnings: 8.90, isActive: true },
              { id: '9', username: 'btc_maxi', joinDate: '2024-03-25', totalEarnings: 15.20, isActive: true },
            ]
          },
          { level: 4, commission: 5, count: 0, earnings: 0, referrals: [] },
          { level: 5, commission: 2, count: 0, earnings: 0, referrals: [] },
        ];
        setReferralLevels(mockLevels);
        const tree = buildTreeStructure(mockLevels);
        setTreeData(tree);
        setExpandedNodes(new Set(['root']));
      }
    } catch (error) {
      console.error('Error loading referral data:', error);
      // Fallback to empty tree
      const emptyLevels: ReferralLevel[] = [
        { level: 1, commission: 40, count: 0, earnings: 0, referrals: [] },
        { level: 2, commission: 15, count: 0, earnings: 0, referrals: [] },
        { level: 3, commission: 10, count: 0, earnings: 0, referrals: [] },
        { level: 4, commission: 5, count: 0, earnings: 0, referrals: [] },
        { level: 5, commission: 2, count: 0, earnings: 0, referrals: [] },
      ];
      setReferralLevels(emptyLevels);
      const tree = buildTreeStructure(emptyLevels);
      setTreeData(tree);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleLevel = (level: number) => {
    const newExpanded = new Set(expandedLevels);
    if (newExpanded.has(level)) {
      newExpanded.delete(level);
    } else {
      newExpanded.add(level);
    }
    setExpandedLevels(newExpanded);
  };

  const toggleNode = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTotalEarnings = () => {
    return referralLevels.reduce((total, level) => total + level.earnings, 0);
  };

  const getTotalReferrals = () => {
    return referralLevels.reduce((total, level) => total + level.count, 0);
  };

  // Component for rendering individual tree nodes
  const TreeNodeComponent: React.FC<{
    node: TreeNode;
    isRoot?: boolean;
    depth?: number;
    isLast?: boolean;
    parentIsLast?: boolean;
  }> = ({ node, isRoot = false, depth = 0, isLast = false, parentIsLast = false }) => {
    const hasChildren = node.children.length > 0;
    const isExpanded = expandedNodes.has(node.id);
    const shouldShowChildren = hasChildren && isExpanded;

    const getCommissionColor = (commission: number) => {
      if (commission >= 40) return 'from-orange-500 to-orange-600';
      if (commission >= 15) return 'from-orange-400 to-orange-500';
      if (commission >= 10) return 'from-yellow-500 to-orange-400';
      if (commission >= 5) return 'from-yellow-400 to-yellow-500';
      return 'from-yellow-300 to-yellow-400';
    };

    return (
      <div className="flex flex-col items-center">
        {/* Node */}
        <div className="flex flex-col items-center relative">
          {/* Connecting line from parent */}
          {!isRoot && (
            <div className="w-0.5 h-8 bg-[#7FFFD4] absolute -top-8"></div>
          )}

          {/* Node circle */}
          <div className="relative">
            <div className={`${
              isRoot
                ? 'w-20 h-20 bg-[#7FFFD4] border-4 border-[#7FFFD4]/20'
                : 'w-14 h-14 bg-[#7FFFD4] border-2 border-[#7FFFD4]/30'
            } rounded-full flex items-center justify-center shadow-lg hover:scale-105 transition-transform cursor-pointer`}
            onClick={() => hasChildren && toggleNode(node.id)}
            >
              <FaUser className={`${isRoot ? 'text-2xl' : 'text-lg'} text-black`} />

              {/* Expand/Collapse indicator */}
              {hasChildren && (
                <div className="absolute -bottom-2 -right-2 w-6 h-6 bg-[#7FFFD4] rounded-full flex items-center justify-center border-2 border-[#141416]">
                  {isExpanded ? (
                    <FaMinus className="text-black text-xs" />
                  ) : (
                    <FaPlus className="text-black text-xs" />
                  )}
                </div>
              )}
            </div>

            {/* Username label */}
            <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-[#141416] px-3 py-1 rounded-lg border border-[#7FFFD4]/30 whitespace-nowrap">
              <span className="text-white font-bold text-sm">{node.username}</span>
            </div>

            {/* Commission badge */}
            {!isRoot && (
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                <div className={`bg-gradient-to-r ${getCommissionColor(node.commission)} text-white px-3 py-1 rounded-full font-bold text-xs shadow-lg border border-orange-400/30`}>
                  {node.commission}%
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Children */}
        {shouldShowChildren && (
          <div className="mt-16 relative">
            {/* Horizontal line to children */}
            {node.children.length > 1 && (
              <div className="absolute top-0 left-0 right-0 h-0.5 bg-[#7FFFD4] transform -translate-y-8"></div>
            )}

            {/* Children nodes */}
            <div className="flex gap-12 items-start">
              {node.children.map((child, index) => (
                <div key={child.id} className="relative">
                  {/* Vertical line to child */}
                  <div className="w-0.5 h-8 bg-[#7FFFD4] absolute top-0 left-1/2 transform -translate-x-0.5 -translate-y-8"></div>

                  <TreeNodeComponent
                    node={child}
                    depth={depth + 1}
                    isLast={index === node.children.length - 1}
                    parentIsLast={isLast}
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <FaSpinner className="animate-spin text-[#7FFFD4] text-3xl mr-4" />
        <span className="text-white text-lg">Loading referral network...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUsers className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Total Network</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">{getTotalReferrals()}</div>
          <p className="text-gray-400 text-sm">Referrals across all levels</p>
        </div>

        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUser className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Network Earnings</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">${getTotalEarnings().toFixed(2)}</div>
          <p className="text-gray-400 text-sm">Total from referral network</p>
        </div>

        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUsers className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Active Levels</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">{referralLevels.filter(l => l.count > 0).length}</div>
          <p className="text-gray-400 text-sm">Levels with referrals</p>
        </div>
      </div>

      {/* Commission Structure */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
        <h3 className="text-xl font-semibold text-white mb-6">Commission Structure</h3>
        <div className="grid grid-cols-5 gap-4">
          {[40, 15, 10, 5, 2].map((commission, index) => (
            <div key={index} className="text-center">
              <div className="bg-[#0F1419] border border-gray-700 rounded-lg p-4 mb-2">
                <div className="text-2xl font-bold text-[#7FFFD4] mb-1">{commission}%</div>
                <div className="text-sm text-gray-400">Level {index + 1}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Interactive Referral Tree */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-8 border border-gray-800/50">
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold text-white mb-2">Referral Network</h3>
          <p className="text-gray-400 text-sm">Your multi-level referral structure</p>
        </div>

        {/* Commission Structure Legend */}
        <div className="flex justify-center mb-8">
          <div className="flex gap-4 flex-wrap justify-center">
            {[
              { level: 1, commission: 40 },
              { level: 2, commission: 15 },
              { level: 3, commission: 10 },
              { level: 4, commission: 5 },
              { level: 5, commission: 2 }
            ].map(({ level, commission }) => (
              <div key={level} className="flex items-center gap-2">
                <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-3 py-1 rounded-full font-bold text-xs shadow-lg border border-orange-400/30">
                  {commission}%
                </div>
                <span className="text-gray-400 text-sm">Level {level}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Tree Visualization */}
        <div className="overflow-x-auto">
          <div className="min-w-max flex justify-center py-8">
            {treeData ? (
              <TreeNodeComponent node={treeData} isRoot={true} />
            ) : (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-[#7FFFD4] rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg border-4 border-[#7FFFD4]/20">
                  <FaUser className="text-black text-2xl" />
                </div>
                <div className="text-white font-bold text-lg mb-2">You</div>
                <div className="text-gray-400 mb-4 text-lg">No referrals yet</div>
                <div className="text-sm text-gray-500 mb-6">Share your referral link to start building your network!</div>
                <div className="text-xs text-gray-600">
                  Earn commissions: 40% → 15% → 10% → 5% → 2%
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Instructions */}
        {treeData && treeData.children.length > 0 && (
          <div className="text-center mt-8 p-4 bg-[#0F1419] rounded-lg border border-gray-700">
            <p className="text-gray-400 text-sm">
              <FaPlus className="inline mr-2 text-[#7FFFD4]" />
              Click on nodes with a + icon to expand and see their referrals
            </p>
          </div>
        )}
      </div>

      {/* Detailed Level Information */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
        <h3 className="text-xl font-semibold text-white mb-6">Detailed Network Information</h3>

        <div className="space-y-4">
          {referralLevels.map((level) => (
            <div key={level.level} className="border border-gray-700 rounded-lg overflow-hidden">
              {/* Level Header */}
              <button
                onClick={() => toggleLevel(level.level)}
                className="w-full px-6 py-4 bg-[#0F1419] hover:bg-[#1A1F24] transition-colors flex items-center justify-between"
              >
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {expandedLevels.has(level.level) ? (
                      <FaChevronDown className="text-gray-400" />
                    ) : (
                      <FaChevronRight className="text-gray-400" />
                    )}
                    <span className="text-white font-semibold">Level {level.level}</span>
                  </div>
                  <div className="flex items-center gap-6">
                    <span className="text-[#7FFFD4] font-semibold">{level.commission}% Commission</span>
                    <span className="text-gray-300">{level.count} Referrals</span>
                    <span className="text-gray-300">${level.earnings.toFixed(2)} Earned</span>
                  </div>
                </div>
              </button>

              {/* Level Content */}
              {expandedLevels.has(level.level) && (
                <div className="p-6 bg-[#141416]">
                  {level.referrals.length > 0 ? (
                    <div className="grid gap-4">
                      {level.referrals.map((referral) => (
                        <div
                          key={referral.id}
                          className="flex items-center justify-between p-4 bg-[#181C20] rounded-lg border border-gray-700"
                        >
                          <div className="flex items-center gap-4">
                            <div className="w-10 h-10 bg-[#7FFFD4]/10 rounded-full flex items-center justify-center">
                              <FaUser className="text-[#7FFFD4]" />
                            </div>
                            <div>
                              <div className="text-white font-medium">{referral.username}</div>
                              <div className="text-gray-400 text-sm">Joined {formatDate(referral.joinDate)}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-white font-semibold">${referral.totalEarnings.toFixed(2)}</div>
                            <div className={`text-sm ${referral.isActive ? 'text-[#7FFFD4]' : 'text-gray-400'}`}>
                              {referral.isActive ? 'Active' : 'Inactive'}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-gray-400 mb-2">No referrals at this level yet</div>
                      <div className="text-sm text-gray-500">Share your referral link to grow your network!</div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ReferralTree;
