import React, { useState, useEffect } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { FaUser, FaUsers, FaChevronDown, FaChevronRight, FaSpinner } from 'react-icons/fa';
import { getRewardsData, ReferralLevel, ReferralUser } from '../api/rewards_api';

// Interfaces are now imported from rewards_api

const ReferralTree = () => {
  const { user } = usePrivy();
  const [expandedLevels, setExpandedLevels] = useState<Set<number>>(new Set([1]));
  const [referralLevels, setReferralLevels] = useState<ReferralLevel[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load referral tree data
  useEffect(() => {
    if (user?.id) {
      loadReferralData();
    }
  }, [user]);

  // Simplified approach - no complex tree building needed

  const loadReferralData = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const response = await getRewardsData(user.id);
      if (response.success && response.data) {
        setReferralLevels(response.data.referralLevels || []);
      } else {
        // Fallback to mock data for demonstration
        const mockLevels: ReferralLevel[] = [
          { level: 1, commission: 40, count: 3, earnings: 450.25, referrals: [] },
          { level: 2, commission: 15, count: 4, earnings: 125.80, referrals: [] },
          { level: 3, commission: 10, count: 2, earnings: 67.40, referrals: [] },
          { level: 4, commission: 5, count: 0, earnings: 0, referrals: [] },
          { level: 5, commission: 2, count: 0, earnings: 0, referrals: [] },
        ];
        setReferralLevels(mockLevels);
      }
    } catch (error) {
      console.error('Error loading referral data:', error);
      // Fallback to empty levels
      const emptyLevels: ReferralLevel[] = [
        { level: 1, commission: 40, count: 0, earnings: 0, referrals: [] },
        { level: 2, commission: 15, count: 0, earnings: 0, referrals: [] },
        { level: 3, commission: 10, count: 0, earnings: 0, referrals: [] },
        { level: 4, commission: 5, count: 0, earnings: 0, referrals: [] },
        { level: 5, commission: 2, count: 0, earnings: 0, referrals: [] },
      ];
      setReferralLevels(emptyLevels);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleLevel = (level: number) => {
    const newExpanded = new Set(expandedLevels);
    if (newExpanded.has(level)) {
      newExpanded.delete(level);
    } else {
      newExpanded.add(level);
    }
    setExpandedLevels(newExpanded);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTotalEarnings = () => {
    return referralLevels.reduce((total, level) => total + level.earnings, 0);
  };

  const getTotalReferrals = () => {
    return referralLevels.reduce((total, level) => total + level.count, 0);
  };

  // Simplified approach - no complex tree component needed

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <FaSpinner className="animate-spin text-[#7FFFD4] text-3xl mr-4" />
        <span className="text-white text-lg">Loading referral network...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUsers className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Total Network</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">{getTotalReferrals()}</div>
          <p className="text-gray-400 text-sm">Referrals across all levels</p>
        </div>

        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUser className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Network Earnings</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">${getTotalEarnings().toFixed(2)}</div>
          <p className="text-gray-400 text-sm">Total from referral network</p>
        </div>

        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#7FFFD4]/10 rounded-lg">
              <FaUsers className="text-[#7FFFD4] text-xl" />
            </div>
            <h3 className="text-lg font-semibold text-white">Active Levels</h3>
          </div>
          <div className="text-3xl font-bold text-white mb-2">{referralLevels.filter(l => l.count > 0).length}</div>
          <p className="text-gray-400 text-sm">Levels with referrals</p>
        </div>
      </div>

      {/* Commission Structure */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
        <h3 className="text-xl font-semibold text-white mb-6">Commission Structure</h3>
        <div className="grid grid-cols-5 gap-4">
          {[40, 15, 10, 5, 2].map((commission, index) => (
            <div key={index} className="text-center">
              <div className="bg-[#0F1419] border border-gray-700 rounded-lg p-4 mb-2">
                <div className="text-2xl font-bold text-[#7FFFD4] mb-1">{commission}%</div>
                <div className="text-sm text-gray-400">Level {index + 1}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Simplified Referral Tree */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-8 border border-gray-800/50">
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold text-white mb-2">Referral Network</h3>
          <p className="text-gray-400 text-sm">Your multi-level referral structure</p>
        </div>

        {/* Commission Structure Legend */}
        <div className="flex justify-center mb-12">
          <div className="flex gap-3 flex-wrap justify-center">
            {[
              { level: 1, commission: 40, color: 'from-orange-500 to-orange-600' },
              { level: 2, commission: 15, color: 'from-orange-400 to-orange-500' },
              { level: 3, commission: 10, color: 'from-yellow-500 to-orange-400' },
              { level: 4, commission: 5, color: 'from-yellow-400 to-yellow-500' },
              { level: 5, commission: 2, color: 'from-yellow-300 to-yellow-400' }
            ].map(({ level, commission, color }) => (
              <div key={level} className="flex items-center gap-2">
                <div className={`bg-gradient-to-r ${color} text-white px-3 py-1 rounded-full font-bold text-sm shadow-lg`}>
                  {commission}%
                </div>
                <span className="text-gray-400 text-sm">Level {level}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Simplified Tree Visualization */}
        <div className="overflow-x-auto">
          <div className="min-w-max flex justify-center py-8">
            <div className="flex flex-col items-center space-y-12">
              {/* You (Root) */}
              <div className="flex flex-col items-center">
                <div className="w-20 h-20 bg-[#7FFFD4] rounded-full flex items-center justify-center shadow-lg border-4 border-[#7FFFD4]/20">
                  <FaUser className="text-black text-2xl" />
                </div>
                <div className="mt-3 text-white font-bold text-lg bg-[#141416] px-4 py-2 rounded-lg border border-[#7FFFD4]/30">
                  You
                </div>
              </div>

              {/* Level 1 */}
              {referralLevels[0] && referralLevels[0].count > 0 && (
                <>
                  <div className="w-0.5 h-8 bg-[#7FFFD4]"></div>
                  <div className="flex flex-col items-center">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg">
                        40%
                      </div>
                      <div className="text-yellow-400 font-bold text-xl">#1</div>
                    </div>
                    <div className="w-16 h-16 bg-[#7FFFD4] rounded-full flex items-center justify-center shadow-lg border-2 border-[#7FFFD4]/30">
                      <span className="text-black font-bold text-lg">{referralLevels[0].count}</span>
                    </div>
                  </div>
                </>
              )}

              {/* Level 2 */}
              {referralLevels[1] && referralLevels[1].count > 0 && (
                <>
                  <div className="w-0.5 h-8 bg-[#7FFFD4]"></div>
                  <div className="flex flex-col items-center">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="bg-gradient-to-r from-orange-400 to-orange-500 text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg">
                        15%
                      </div>
                      <div className="text-yellow-400 font-bold text-xl">#2</div>
                    </div>
                    <div className="w-16 h-16 bg-[#7FFFD4] rounded-full flex items-center justify-center shadow-lg border-2 border-[#7FFFD4]/30">
                      <span className="text-black font-bold text-lg">{referralLevels[1].count}</span>
                    </div>
                  </div>
                </>
              )}

              {/* Level 3 */}
              {referralLevels[2] && referralLevels[2].count > 0 && (
                <>
                  <div className="w-0.5 h-8 bg-[#7FFFD4]"></div>
                  <div className="flex flex-col items-center">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="bg-gradient-to-r from-yellow-500 to-orange-400 text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg">
                        10%
                      </div>
                      <div className="text-yellow-400 font-bold text-xl">#3</div>
                    </div>
                    <div className="w-16 h-16 bg-[#7FFFD4] rounded-full flex items-center justify-center shadow-lg border-2 border-[#7FFFD4]/30">
                      <span className="text-black font-bold text-lg">{referralLevels[2].count}</span>
                    </div>
                  </div>
                </>
              )}

              {/* Level 4 */}
              {referralLevels[3] && referralLevels[3].count > 0 && (
                <>
                  <div className="w-0.5 h-8 bg-[#7FFFD4]"></div>
                  <div className="flex flex-col items-center">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg">
                        5%
                      </div>
                      <div className="text-yellow-400 font-bold text-xl">#4</div>
                    </div>
                    <div className="w-16 h-16 bg-[#7FFFD4] rounded-full flex items-center justify-center shadow-lg border-2 border-[#7FFFD4]/30">
                      <span className="text-black font-bold text-lg">{referralLevels[3].count}</span>
                    </div>
                  </div>
                </>
              )}

              {/* Level 5 */}
              {referralLevels[4] && referralLevels[4].count > 0 && (
                <>
                  <div className="w-0.5 h-8 bg-[#7FFFD4]"></div>
                  <div className="flex flex-col items-center">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="bg-gradient-to-r from-yellow-300 to-yellow-400 text-black px-4 py-2 rounded-full font-bold text-sm shadow-lg">
                        2%
                      </div>
                      <div className="text-yellow-400 font-bold text-xl">#5</div>
                    </div>
                    <div className="w-16 h-16 bg-[#7FFFD4] rounded-full flex items-center justify-center shadow-lg border-2 border-[#7FFFD4]/30">
                      <span className="text-black font-bold text-lg">{referralLevels[4].count}</span>
                    </div>
                  </div>
                </>
              )}

              {/* Empty State */}
              {referralLevels.every(level => level.count === 0) && (
                <div className="text-center py-8">
                  <div className="text-gray-400 mb-4 text-lg">No referrals yet</div>
                  <div className="text-sm text-gray-500 mb-6">Share your referral link to start building your network!</div>
                  <div className="text-xs text-gray-600">
                    Earn commissions: 40% → 15% → 10% → 5% → 2%
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Level Information */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
        <h3 className="text-xl font-semibold text-white mb-6">Detailed Network Information</h3>

        <div className="space-y-4">
          {referralLevels.map((level) => (
            <div key={level.level} className="border border-gray-700 rounded-lg overflow-hidden">
              {/* Level Header */}
              <button
                onClick={() => toggleLevel(level.level)}
                className="w-full px-6 py-4 bg-[#0F1419] hover:bg-[#1A1F24] transition-colors flex items-center justify-between"
              >
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {expandedLevels.has(level.level) ? (
                      <FaChevronDown className="text-gray-400" />
                    ) : (
                      <FaChevronRight className="text-gray-400" />
                    )}
                    <span className="text-white font-semibold">Level {level.level}</span>
                  </div>
                  <div className="flex items-center gap-6">
                    <span className="text-[#7FFFD4] font-semibold">{level.commission}% Commission</span>
                    <span className="text-gray-300">{level.count} Referrals</span>
                    <span className="text-gray-300">${level.earnings.toFixed(2)} Earned</span>
                  </div>
                </div>
              </button>

              {/* Level Content */}
              {expandedLevels.has(level.level) && (
                <div className="p-6 bg-[#141416]">
                  {level.referrals.length > 0 ? (
                    <div className="grid gap-4">
                      {level.referrals.map((referral) => (
                        <div
                          key={referral.id}
                          className="flex items-center justify-between p-4 bg-[#181C20] rounded-lg border border-gray-700"
                        >
                          <div className="flex items-center gap-4">
                            <div className="w-10 h-10 bg-[#7FFFD4]/10 rounded-full flex items-center justify-center">
                              <FaUser className="text-[#7FFFD4]" />
                            </div>
                            <div>
                              <div className="text-white font-medium">{referral.username}</div>
                              <div className="text-gray-400 text-sm">Joined {formatDate(referral.joinDate)}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-white font-semibold">${referral.totalEarnings.toFixed(2)}</div>
                            <div className={`text-sm ${referral.isActive ? 'text-[#7FFFD4]' : 'text-gray-400'}`}>
                              {referral.isActive ? 'Active' : 'Inactive'}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-gray-400 mb-2">No referrals at this level yet</div>
                      <div className="text-sm text-gray-500">Share your referral link to grow your network!</div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ReferralTree;
