# Rewards Module

This module implements a comprehensive referral and rewards system for the RedFyn platform.

## Features

### 1. Dashboard
- **Referral Code Input**: Users can enter and save someone else's referral code
- **Personal Referral Link**: Auto-generated unique referral link with copy functionality
- **Rewards Overview**: Display unclaimed rewards, total referrals, and total earned
- **Claim Functionality**: Integrated with Solana wallet selection for claiming rewards

### 2. Referral Tree
- **5-Level Commission Structure**: 40%, 15%, 10%, 5%, 2% for levels 1-5
- **Visual Tree Display**: Expandable/collapsible levels with referral details
- **Network Statistics**: Total network size and earnings overview
- **User Details**: Individual referral information with join dates and activity status

### 3. Claims History
- **Detailed Transaction History**: Complete record of all reward claims
- **Advanced Filtering**: Filter by status, type, and date range
- **Sorting Options**: Sort by date, amount, or other fields
- **Transaction Links**: Direct links to Solana blockchain explorer

## Components

### Core Components
- `Rewards.tsx` - Main container component
- `RewardsTabs.tsx` - Tab navigation component
- `Dashboard.tsx` - Main dashboard with referral input and overview
- `ReferralTree.tsx` - Referral network visualization
- `ClaimsHistory.tsx` - Transaction history and reporting
- `ClaimRewardsModal.tsx` - Wallet selection and claiming interface

### API Integration
- `rewards_api.ts` - Complete API service layer for rewards functionality
- Integrated with existing Privy wallet system
- Real-time data loading and caching

## Design Consistency

The module follows the existing RedFyn design patterns:
- Color scheme: `#141416` background, `#181C20` cards, `#7FFFD4` accents
- Typography: Consistent with Portfolio and Pulse sections
- Responsive design: Mobile-first approach with breakpoints
- Component styling: Matches existing UI patterns

## Navigation Integration

- Added "Rewards" to main navbar alongside "Pulse" and "Portfolio"
- Proper routing configuration in `App.tsx`
- State management for active tab persistence

## API Endpoints

The module expects the following backend endpoints:
- `POST /api/rewards/save-referral-code` - Save user's referral code
- `GET /api/rewards/data/{userId}` - Get user's rewards data and referral tree
- `POST /api/rewards/claim` - Claim rewards to selected wallet
- `GET /api/rewards/claims-history/{userId}` - Get claims history with filtering

## Usage

The Rewards module is automatically available in the main navigation. Users can:
1. Enter referral codes to link their account
2. Generate and share their personal referral link
3. View their referral network and earnings
4. Claim rewards to their Solana wallets
5. Track their complete claims history

## Dependencies

- React 18+
- TypeScript
- Privy SDK for wallet integration
- React Router for navigation
- React Toastify for notifications
- Tailwind CSS for styling
