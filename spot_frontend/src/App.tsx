import * as React from 'react';
import { useEffect, useState, useC<PERSON>back, ReactNode, ErrorInfo } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { usePrivy, useWallets } from '@privy-io/react-auth';
import './App.css';
import { transactionLogger, LogSeverity } from './utils/transactionLogger';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import SmartWalletInitializer from './components/SmartWalletInitializer';
import SessionSignerInitializer from './components/SessionSignerInitializer';
import Pulse from './Pulse/Pulse';
import Portfolio from './Portfolio/Portfolio';
import Rewards from './Rewards/Rewards';
import PulseTrade from './Pulse_Trade/PulseTrade';

import 'react-toastify/dist/ReactToastify.css';
// Add Phantom type definition to Window interface
declare global {
  interface Window {
    phantom?: {
      solana?: {
        isConnected?: boolean;
        connect: (options?: { onlyIfTrusted?: boolean }) => Promise<{ publicKey: { toString: () => string } }>;
      };
    };
    getWalletInfo?: () => any;
    walletInitializationComplete: boolean;
    walletRequestsInProgress: () => boolean;
    DISABLE_AUTO_CONNECT: boolean; // Add flag to disable auto-connection
  }
}

// Set flag to disable auto connection
window.DISABLE_AUTO_CONNECT = true;

// Import Solana-specific hooks - compatible with Privy 2.0
let useSendSolanaTransaction: any;
try {
  // This is the proper import path for Solana functions in Privy 2.0
  const solanaModule = require('@privy-io/react-auth/solana');
  useSendSolanaTransaction = solanaModule.useSendTransaction;
} catch (e: any) {
  console.warn("Couldn't import Solana-specific hooks:", e.message);
  // Create a dummy implementation as fallback
  useSendSolanaTransaction = () => ({
    sendTransaction: async () => {
      console.error("Solana transaction sending not available");
      throw new Error("Solana transaction sending not available");
    }
  });
}

// Define interfaces for the error boundary
interface ErrorBoundaryProps {
  children: React.ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

// Simple error boundary component to catch Privy errors
class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error("Error caught by boundary:", error, errorInfo);
  }

  render(): React.ReactNode {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-[#141416] text-white flex-col">
          <h2 className="text-xl mb-4">Something went wrong</h2>
          <p className="text-md text-red-400 mb-6">
            {this.state.error?.message || "An unexpected error occurred in the authentication system"}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
          >
            Reload Application
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Define interfaces for wallet data
interface WalletAddress {
  address: string;
  isEthereum: boolean;
  isSolana: boolean;
}

function App(): JSX.Element {
  const {
    ready,
    authenticated,
    user,
    login,
    logout,
    linkEmail,
    linkWallet,
    linkPhone,
    linkGoogle,
    linkTwitter,
    linkDiscord,
    linkGithub,
    unlinkEmail,
    unlinkWallet,
    unlinkPhone,
    unlinkGoogle,
    unlinkTwitter,
    unlinkDiscord,
    unlinkGithub,
    exportWallet,
    connectWallet,
    signMessage,
  } = usePrivy();
  const { wallets } = useWallets();
  const [hasSolanaAddress, setHasSolanaAddress] = useState<boolean>(false);
  const [connectedWallets, setConnectedWallets] = useState<any[]>([]);

  // Check for Solana address on mount and when wallets change
  useEffect(() => {
    const checkSolanaAddress = () => {
      // Skip if auto-connect is disabled
      if (window.DISABLE_AUTO_CONNECT) {
        console.log("App: Auto wallet connection disabled, skipping automatic wallet discovery");
        return;
      }

      // Don't run this logic until initialization is complete
      const isWalletInitComplete = window.walletInitializationComplete === true;

      if (!isWalletInitComplete || !wallets) {
        console.log("App: Skipping wallet check - initialization not complete");
        return;
      }

      if (wallets && wallets.length > 0) {
        console.log("App: Checking wallets for Solana addresses:", wallets.length);

        const walletAddresses: WalletAddress[] = [];
        let foundSolana = false;
        let foundSmartWallet = false;

        wallets.forEach(wallet => {
          if (wallet && wallet.address) {
            // Check wallet type
            const isSmartWallet = wallet.walletClientType === 'privy' ||
                                 wallet.walletClientType === 'smart' ||
                                 (wallet as any).type === 'smart_wallet';

            if (isSmartWallet) {
              foundSmartWallet = true;
              console.log("Found smart wallet:", wallet.address);
            }

            // Track in our connected wallets list
            walletAddresses.push({
              address: wallet.address,
              isEthereum: wallet.address.startsWith('0x'),
              isSolana: !wallet.address.startsWith('0x')
            });

            // Check if this is a Solana address
            if (!wallet.address.startsWith('0x')) {
              console.log("Found Solana address:", wallet.address);
              foundSolana = true;
            }
          }
        });

        setConnectedWallets(walletAddresses);

        // If we found a Solana address, or a smart wallet, consider wallet setup complete
        if (foundSolana || foundSmartWallet) {
          setHasSolanaAddress(foundSolana);
          // Store the first smart wallet as the default
          if (foundSmartWallet) {
            const smartWallet = wallets.find(w =>
              w.walletClientType === 'privy' ||
              w.walletClientType === 'smart' ||
              (w as any).type === 'smart_wallet'
            );

            if (smartWallet) {
              console.log("Setting default smart wallet:", smartWallet.address);
              localStorage.setItem('selected_wallet', smartWallet.address);
            }
          }

          // Prevent any automatic connection attempts
          console.log("Found valid wallets, no need for external wallet connection");
        } else if (hasSolanaAddress) {
          console.log("Solana address available but not connected - wait for user to explicitly connect");

          // Check if we have a previously stored Phantom wallet
          const storedPhantomWallet = localStorage.getItem('phantom_wallet');
          if (storedPhantomWallet) {
            try {
              const phantomData = JSON.parse(storedPhantomWallet);
              if (phantomData && phantomData.address) {
                console.log("Using previously connected Phantom wallet:", phantomData.address);
                setHasSolanaAddress(true);
              }
            } catch (e) {
              console.error("Error parsing stored Phantom wallet data:", e);
            }
          }
        }
      }
    };

    checkSolanaAddress();
  }, [wallets, authenticated, hasSolanaAddress]);

  // Add a new useEffect to listen for wallet setup completion
  useEffect(() => {
    const handleWalletSetupComplete = (event: any) => {
      console.log("App: Wallet setup complete event received", event.detail);

      // Trigger wallet discovery without connection attempts
      if (wallets && wallets.length > 0) {
        console.log("App: Triggering wallet discovery after setup completion");
        setConnectedWallets(prev => [...prev]); // Trigger re-render without changing data
      }
    };

    window.addEventListener('wallet-setup-complete', handleWalletSetupComplete);

    return () => {
      window.removeEventListener('wallet-setup-complete', handleWalletSetupComplete);
    };
  }, [wallets]);

  // Function to try connecting Phantom wallet directly - only when explicitly requested
  const tryConnectPhantomDirectly = useCallback(async () => {
    // Don't attempt connection if wallet initialization isn't complete
    const isWalletInitComplete = window.walletInitializationComplete === true;
    if (!isWalletInitComplete) {
      console.log("App: Skipping Phantom connection - wallet initialization not complete");
      return;
    }

    if (!window.phantom?.solana) return;

    try {
      console.log("Attempting direct Phantom connection");
      const phantomProvider = window.phantom.solana;

      // Check if already connected
      if (phantomProvider.isConnected) {
        console.log("Phantom is already connected, getting public key");
        try {
          const { publicKey } = await phantomProvider.connect({ onlyIfTrusted: true });
          if (publicKey) {
            const solanaAddress = publicKey.toString();
            console.log("Got Solana address from Phantom:", solanaAddress);
            setHasSolanaAddress(true);

            // Dispatch an event that our components can listen for
            window.dispatchEvent(new CustomEvent('solana-wallet-connected', {
              detail: { address: solanaAddress }
            }));

            // Also store in localStorage for persistence
            const phantomData = {
              address: solanaAddress,
              type: 'solana',
              timestamp: Date.now()
            };
            localStorage.setItem('phantom_wallet', JSON.stringify(phantomData));
          }
        } catch (e) {
          console.log("Error getting connected wallet, will try regular connect:", e);
          // Don't automatically try regular connect to avoid wallet popups
          console.log("Skipping automatic connection to avoid unwanted popups");
        }
      } else {
        console.log("Phantom not connected - this function should only be called on explicit user action");
      }
    } catch (err) {
      console.error("Error connecting to Phantom directly:", err);
    }
  }, []);

  // Full Phantom connection flow
  const tryConnectPhantomFull = async () => {
    if (!window.phantom?.solana) return;

    try {
      // Try to connect through Privy first using updated methods for Solana
      try {
        console.log("Trying to connect Phantom via Privy for Solana...");

        // Force Solana chain with Phantom - explicitly specify details
            if (wallets && wallets.length > 0) {
              // Look specifically for Solana addresses (non-0x)
              const solanaWallet = wallets.find(w =>
                w && w.address && !w.address.startsWith('0x')
              );

              if (solanaWallet) {
                console.log("Successfully got Solana wallet via Privy:", solanaWallet.address);
                setHasSolanaAddress(true);

                // Store in localStorage
                const phantomData = {
                  address: solanaWallet.address,
                  type: 'solana',
                  timestamp: Date.now()
                };
                localStorage.setItem('phantom_wallet', JSON.stringify(phantomData));
              } else {
                console.log("Connected to Phantom but didn't get a Solana address, falling back to direct connect");
                // If we didn't get a Solana address, try direct connection
                await connectPhantomDirectly();
              }
        }
      } catch (e) {
        console.log("Could not connect via Privy, trying direct connect:", e);
        await connectPhantomDirectly();
      }
    } catch (err) {
      console.error("Error in tryConnectPhantomFull:", err);
    }
  };

  // Connect directly to Phantom for Solana
  const connectPhantomDirectly = async () => {
    try {
      // Use the direct Phantom API to get the Solana address
      if (!window.phantom?.solana) return;
      const phantomProvider = window.phantom.solana;
      const { publicKey } = await phantomProvider.connect();

      if (publicKey) {
        const solanaAddress = publicKey.toString();
        console.log("Got Solana address from direct Phantom connection:", solanaAddress);
        setHasSolanaAddress(true);

        // Dispatch an event that our components can listen for
        window.dispatchEvent(new CustomEvent('solana-wallet-connected', {
          detail: { address: solanaAddress }
        }));

        // Store in localStorage
        const phantomData = {
          address: solanaAddress,
          type: 'solana',
          timestamp: Date.now()
        };
        localStorage.setItem('phantom_wallet', JSON.stringify(phantomData));
      }
    } catch (error) {
      console.error("Error connecting to Phantom directly:", error);
    }
  };

  // Ensure wallets info is added to window for debugging
  useEffect(() => {
    // Handle errors in authentication system
    const handleError = (event: ErrorEvent) => {
      console.error("JS Error caught:", event.error);

      // Only handle errors that seem related to wallet connections
      if (event.error && typeof event.error.message === 'string' &&
          (event.error.message.includes('wallet') ||
           event.error.message.includes('Wallet') ||
           event.error.message.includes('Privy') ||
           event.error.message.includes('privy'))) {
        setHasSolanaAddress(false);
      }
    };

    window.addEventListener('error', handleError);

    // For debugging only - add wallet info to window
    window.getWalletInfo = () => {
      return {
        authenticated,
        wallets: wallets?.map(w => {
          // Using any type to avoid ConnectedWallet property errors
          const wallet = w as any;
          return {
            address: wallet.address,
            chainId: wallet.chainId,
            // Use only properties that are definitely available
            walletClientType: wallet.walletClientType
          };
        }),
        connectedWallets,
        hasSolanaAddress
      };
    };

    return () => {
      window.removeEventListener('error', handleError);
      if (window.getWalletInfo) {
        delete window.getWalletInfo;
      }
    };
  }, [authenticated, wallets, connectedWallets, hasSolanaAddress]);

  // Handle logout with cleanup
  const handleCompleteLogout = () => {
    // Clear localStorage wallet data
    localStorage.removeItem('phantom_wallet');
    localStorage.removeItem('selected_wallet');

    // Reset state
    setHasSolanaAddress(false);
    setConnectedWallets([]);

    // Reload the page to completely reset application state
    window.location.reload();
  };

  // Show loading state when we're waiting for Privy to initialize
  if (!ready) {
    return <div className="min-h-screen flex items-center justify-center bg-[#141416] text-white">Loading...</div>;
  }

  // Show login button if not authenticated
  if (!authenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#141416]">
        <div>
        <button
          onClick={() => {
            // Clear any stale tokens before login
            localStorage.removeItem('privy:token');
            // Use login function for initial authentication
            login();
          }}
          className="px-6 py-3 text-lg font-semibold text-white bg-[#214638] rounded-lg hover:bg-[#14FFA2] hover:text-black transition-colors"
        >
          Connect Wallet
        </button>
        </div>
      </div>
    );
  }

  // Main application with routing
  return (
    <ErrorBoundary>
      <BrowserRouter>
        <div className="App bg-[#141416] max-h-screen">
          {/* Initialize Smart Wallet */}
          <SmartWalletInitializer />

          {/* Initialize Session Signers */}
          <SessionSignerInitializer />

          {/* Routes */}
          <Routes>
            <Route path="/" element={<Navigate to="/pulse" replace />} />
            <Route path="/pulse" element={<Pulse/>} />
            <Route path="/portfolio" element={<Portfolio/>} />
            <Route path="/rewards" element={<Rewards/>} />
            <Route path="/trade/:address" element={<PulseTrade/>} />
            <Route path="/pulse-trade" element={<PulseTrade/>} />
            <Route path="/pulse-trade/:address" element={<PulseTrade/>} />
            <Route path="*" element={<Navigate to="/pulse" replace />} />
          </Routes>
        </div>
      </BrowserRouter>
      
      {/* Add ToastContainer for notifications */}
      <ToastContainer position="bottom-right" theme="dark" />
    </ErrorBoundary>
  );
}

export default App;
